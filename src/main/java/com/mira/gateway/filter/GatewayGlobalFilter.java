package com.mira.gateway.filter;

import com.mira.api.iam.provider.IAuthProvider;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.BizCodeEnum;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.response.CommonResult;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.SpringContextHolder;
import com.mira.gateway.properties.ApiBlackWhiteProperties;
import com.mira.gateway.properties.SwaggerProperties;
import com.mira.gateway.util.DingTalkTextMsgUtil;
import com.mira.gateway.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;

/**
 * 全局过滤器
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class GatewayGlobalFilter implements GlobalFilter, Ordered {
    @Resource
    private ApiBlackWhiteProperties apiBlackWhiteProperties;
    @Resource
    private SwaggerProperties swaggerProperties;

    /**
     * 预编译的正则表达式缓存
     */
    private final ConcurrentHashMap<String, Pattern> patternCache = new ConcurrentHashMap<>();

    /**
     * 认证超时时间
     */
    private static final Duration AUTH_TIMEOUT = Duration.ofSeconds(10);

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        String path = request.getURI().getPath();

        String clientIp = IpUtil.getClientIp(request);
        long startTime = System.currentTimeMillis();

        // Api Doc
        if (!swaggerProperties.isProduction() && matchApiDoc(path)) {
            return chain.filter(exchange);
        }
        // 白名单
        if (matchWhite(path)) {
            return chain.filter(exchange);
        }
        // 黑名单
        if (matchBlack(path)) {
            return wrapMono(response, CommonResult.FAILED(HttpStatus.FORBIDDEN.value(), HttpStatus.FORBIDDEN.getReasonPhrase()));
        }

        // 获取令牌，获取用户类别
        AtomicReference<String> userTypeRef = new AtomicReference<>();
        String authorization = getAuthorization(request, userTypeRef);
        if (StringUtils.isEmpty(authorization)) {
            return wrapMono(response, CommonResult.FAILED(BizCodeEnum.TOKEN_INVALID.getCode(), BizCodeEnum.TOKEN_INVALID.getMsg()));
        }

        // 鉴权
        return Mono.fromCallable(() -> SpringContextHolder.getBean(IAuthProvider.class).convertToken(authorization, userTypeRef.get()))
                .subscribeOn(Schedulers.boundedElastic())
                .timeout(AUTH_TIMEOUT)
                .flatMap(iamResult -> {
                    if (BizCodeEnum.SUCCESS.getCode() != iamResult.getCode()) {
                        return wrapMono(response, CommonResult.FAILED(BizCodeEnum.TOKEN_INVALID.getCode(), BizCodeEnum.TOKEN_INVALID.getMsg()));
                    }

                    String jwt = iamResult.getData().getAccess_token();
                    // 设置请求头
                    ServerHttpRequest mutateRequest = request.mutate()
                            .header(HeaderConst.AUTHORIZATION, jwt)
                            .header(HeaderConst.USER_TYPE, userTypeRef.get())
                            .build();
                    ServerWebExchange mutateExchange = exchange.mutate().request(mutateRequest).build();

                    return chain.filter(mutateExchange)
                            .doOnSuccess(aVoid -> logPrint(path, userTypeRef.get(), startTime, clientIp, "success", "request success"))
                            .doOnError(error -> logPrint(path, userTypeRef.get(), startTime, clientIp, "fail", error.getMessage()));

                }).onErrorResume(throwable -> {
                    logPrint(path, userTypeRef.get(), startTime, clientIp, "error", throwable.getMessage());
                    DingTalkTextMsgUtil.sendDingTalk(authorization, path, "gateway receive error: " + throwable.getMessage(), true);
                    return wrapMono(response, CommonResult.FAILED(BizCodeEnum.INTERNAL_SERVER_ERROR.getCode(), BizCodeEnum.INTERNAL_SERVER_ERROR.getMsg()));
                });
    }

    @Override
    public int getOrder() {
        return Integer.MIN_VALUE;
    }

    private boolean matchApiDoc(String path) {
        return path.contains("v3/api-docs");
    }

    /**
     * 匹配白名单路径
     */
    private boolean matchWhite(String path) {
        try {
            String[] whiteList = apiBlackWhiteProperties.getApiWhite();
            if (whiteList == null) {
                return false;
            }

            for (String white : whiteList) {
                if (StringUtils.isEmpty(white)) {
                    continue;
                }

                if (path.equals(white)) {
                    return true;
                }

                if (white.endsWith("*")) {
                    Pattern pattern = getOrCreatePattern(white);
                    if (pattern != null && pattern.matcher(path).matches()) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Error matching white list for path: {}, error: {}", path, e.getMessage());
            return false;
        }
        return false;
    }

    /**
     * 匹配黑名单路径
     */
    private boolean matchBlack(String path) {
        try {
            String[] blackList = apiBlackWhiteProperties.getApiBlack();
            if (blackList == null) {
                return false;
            }

            for (String black : blackList) {
                if (StringUtils.isEmpty(black)) {
                    continue;
                }

                if (path.equals(black)) {
                    return true;
                }

                if (black.endsWith("*")) {
                    Pattern pattern = getOrCreatePattern(black);
                    if (pattern != null && pattern.matcher(path).matches()) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Error matching black list for path: {}, error: {}", path, e.getMessage());
            return false;
        }
        return false;
    }

    private Pattern getOrCreatePattern(String patternStr) {
        return patternCache.computeIfAbsent(patternStr, key -> {
            try {
                String regex = key.substring(0, key.length() - 1) + ".+";
                return Pattern.compile(regex);
            } catch (Exception e) {
                log.warn("Failed to compile pattern: {}, error: {}", key, e.getMessage());
                return null;
            }
        });
    }

    private Mono<Void> wrapMono(ServerHttpResponse response, CommonResult<String> commonResult) {
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        DataBuffer dataBuffer = response.bufferFactory().wrap(JsonUtil.toJson(commonResult).getBytes());

        return response.writeWith(Flux.just(dataBuffer));
    }

    /**
     * 获取授权令牌和用户类型
     */
    private String getAuthorization(ServerHttpRequest request, AtomicReference<String> userTypeRef) {
        for (UserTypeEnum userTypeEnum : UserTypeEnum.values()) {
            for (String headerName : userTypeEnum.getTokenHeaderList()) {
                String authorization = request.getHeaders().getFirst(headerName);
                if (StringUtils.isNotEmpty(authorization)) {
                    userTypeRef.set(userTypeEnum.getType());
                    return authorization;
                }
            }
        }
        return null;
    }

    /**
     * 打印请求日志
     */
    private void logPrint(String path, String userType, long startTime, String clientIp, String status, String message) {
        log.info("path: {}, user type: {}, duration: {}ms, cient ip: {}, status: {}, message: {}",
                path, userType, System.currentTimeMillis() - startTime, clientIp, status, message);
    }
}
